create table brands (
  id          bigint generated always as identity primary key,
  name        text not null unique
);

create table categories (
  id          bigint generated always as identity primary key,
  name        text not null unique
);

create table products (
  id                  text primary key,
  name                text         not null,
  size                text,
  brand_id            bigint       references brands(id),
  category_id         bigint       references categories(id),
  subcategory         text,
  unit_price          numeric(10,2),
  unit_name           text,
  original_unit_qty   numeric(10,2),
  source_site         text,
  last_updated        timestamptz  not null,
  last_checked        timestamptz  not null,
  created_at          timestamptz  default now()
);

create index on products(category_id);
create index on products(brand_id);

create table stores (
  id          bigint generated always as identity primary key,
  name        text not null unique,
  logo_url    text
);

create table prices (
  id            bigint generated always as identity primary key,
  product_id    text   references products(id) on delete cascade,
  store_id      bigint references stores(id)   on delete cascade,
  price         numeric(10,2) not null,
  recorded_at   timestamptz   not null default now()
);

create index on prices(product_id, store_id);
create index on prices(recorded_at);

alter table prices enable row level security;
create policy "Public read access to prices"
  on prices
  for select
  using ( true ); 