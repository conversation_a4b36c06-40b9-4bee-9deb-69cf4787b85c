-- Fix products table schema to match scraper expectations
-- This handles extra columns that might exist with NOT NULL constraints

-- First, let's check and fix any problematic NOT NULL columns
DO $$
BEGIN
    -- If 'store' column exists and has NOT NULL constraint, make it nullable
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'store'
    ) THEN
        -- Drop NOT NULL constraint if it exists
        ALTER TABLE products ALTER COLUMN store DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from store column';
    END IF;
    
    -- Ensure original_unit_qty column exists (from previous migration)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'original_unit_qty'
    ) THEN
        ALTER TABLE products ADD COLUMN original_unit_qty numeric(10,2);
        RAISE NOTICE 'Added original_unit_qty column to products table';
    END IF;
    
    -- Check if we have the expected core columns and add them if missing
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'source_site'
    ) THEN
        ALTER TABLE products ADD COLUMN source_site text;
        RAISE NOTICE 'Added source_site column to products table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'last_updated'
    ) THEN
        ALTER TABLE products ADD COLUMN last_updated timestamptz NOT NULL DEFAULT now();
        RAISE NOTICE 'Added last_updated column to products table';
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'last_checked'
    ) THEN
        ALTER TABLE products ADD COLUMN last_checked timestamptz NOT NULL DEFAULT now();
        RAISE NOTICE 'Added last_checked column to products table';
    END IF;
    
    RAISE NOTICE 'Products table schema fixes completed';
END
$$;

-- Drop and recreate products table with correct schema if it's completely wrong
-- This is a more drastic approach - uncomment if needed
/*
DROP TABLE IF EXISTS products CASCADE;
CREATE TABLE products (
  id                  text primary key,
  name                text         not null,
  size                text,
  brand_id            bigint       references brands(id),
  category_id         bigint       references categories(id),
  subcategory         text,
  unit_price          numeric(10,2),
  unit_name           text,
  original_unit_qty   numeric(10,2),
  source_site         text,
  last_updated        timestamptz  not null,
  last_checked        timestamptz  not null,
  created_at          timestamptz  default now()
);

CREATE INDEX ON products(category_id);
CREATE INDEX ON products(brand_id);
*/