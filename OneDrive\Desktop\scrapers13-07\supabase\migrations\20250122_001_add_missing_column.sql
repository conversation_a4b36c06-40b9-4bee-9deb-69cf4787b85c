-- Add missing original_unit_qty column if it doesn't exist
-- This fixes the error: column "original_unit_qty" of relation "products" does not exist

DO $$
BEGIN
    -- Check if column exists before adding it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'original_unit_qty'
    ) THEN
        ALTER TABLE products ADD COLUMN original_unit_qty numeric(10,2);
        RAISE NOTICE 'Added original_unit_qty column to products table';
    ELSE
        RAISE NOTICE 'Column original_unit_qty already exists';
    END IF;
END
$$;