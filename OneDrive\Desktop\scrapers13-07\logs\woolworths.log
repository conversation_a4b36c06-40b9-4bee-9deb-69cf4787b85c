
> cd-scraper@1.0.0 db-images
> npx esrun src/index.ts db images

[dotenv@17.2.0] injecting env (7) from .env (tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit)
[dotenv@17.2.0] injecting env (0) from .env.local (tip: 🔐 prevent committing .env to code: https://dotenvx.com/precommit)
[dotenv@17.2.0] injecting env (0) from .env (tip: 🛠️  run anywhere with `dotenvx run -- yourcommand`)
[dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  override existing env vars with { override: true })
[dotenv@17.2.0] injecting env (0) from .env.local (tip: ⚙️  enable debug logging with { debug: true })
[33m🔄 MongoDB connection attempt 1/5...[0m
[32m✅ MongoDB connection established[0m
[38;5;117m✅ MongoDB indexes created[0m
[32m✅ Consolidated products MongoDB initialized[0m
[33mLaunching Browser.. (2 arguments found)[0m
[33mAttempting to select store location: Auckland Central[0m
[33mNo store selector found - proceeding with default location[0m
[33m260 pages to be scraped            7s delay between scrapes           (Database Mode)[0m
[33m
[1/260] www.woolworths.co.nz/shop/browse/fruit-veg?page=1[0m
[33m52 product entries found           Time Elapsed: 40s                  Category: Fruit Veg[0m
[32m  Upserted: Salad Toppers Salad Sprinkles Crispy Onions C | $5.3[0m
[31mMongoDB upsert failed: Cannot read properties of undefined (reading 'PriceChanged')[0m
[31mPage Timeout after 30 seconds - Skipping this page
TypeError: Cannot read properties of undefined (reading 'Failed')[0m
[33m
[2/260] www.woolworths.co.nz/shop/browse/fruit-veg?page=2[0m
[31mPage Timeout after 30 seconds - Skipping this page
TimeoutError: page.waitForSelector: Timeout 30000ms exceeded.
Call log:
[2m  - waiting for locator('product-price h3') to be visible[22m
[0m
[33m
[3/260] www.woolworths.co.nz/shop/browse/fruit-veg?page=3[0m
[33m48 product entries found           Time Elapsed: 1:25                 Category: Fruit Veg[0m
[32m  Upserted: Carrots Petite Baby Prepacked                 | $3.49[0m
[31mMongoDB upsert failed: Cannot read properties of undefined (reading 'PriceChanged')[0m
[31mPage Timeout after 30 seconds - Skipping this page
TypeError: Cannot read properties of undefined (reading 'Failed')[0m
[33m
[4/260] www.woolworths.co.nz/shop/browse/fruit-veg?page=4[0m
