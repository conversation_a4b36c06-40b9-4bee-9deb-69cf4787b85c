{"version": 2, "dgSpecHash": "mFaB1I5iYtg=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\tests\\PakScraperTests.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\' doesn't exist.", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\tests\\PakScraperTests.csproj", "filePath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\tests\\PakScraperTests.csproj", "libraryId": "Npgsql", "targetGraphs": []}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\' doesn't exist.", "projectPath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\tests\\PakScraperTests.csproj", "filePath": "C:\\Users\\<USER>\\OneDrive\\Desktop\\scrapers13-07\\paknsave\\tests\\PakScraperTests.csproj", "libraryId": "<PERSON><PERSON>", "targetGraphs": []}]}