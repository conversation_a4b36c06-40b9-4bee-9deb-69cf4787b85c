#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();
require('dotenv').config({ path: `.env.local`, override: true });

async function checkDatabaseStructure() {
  console.log('🔍 Analyzing actual database structure...');
  
  const url = process.env.SUPABASE_URL;
  const key = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
  
  if (!url || !key) {
    console.error('❌ SUPABASE_URL or SUPABASE_ANON_KEY not set in environment');
    process.exit(1);
  }

  const supabase = createClient(url, key, { auth: { persistSession: false } });

  try {
    console.log('📊 Checking products table structure...');
    
    // Try to get column information
    const { data: columns, error: colError } = await supabase.rpc('get_table_columns', {
      table_name: 'products'
    });

    if (colError && colError.message.includes('function get_table_columns() does not exist')) {
      console.log('⚠️ Custom function not available, using direct query approach...');
      
      // Alternative approach: try to get a sample record to understand structure
      const { data: sampleData, error: sampleError } = await supabase
        .from('products')
        .select('*')
        .limit(1);

      if (sampleError) {
        console.error('❌ Error querying products table:', sampleError.message);
        console.log('\n🔧 Potential solutions:');
        console.log('1. Check if products table exists in your Supabase project');
        console.log('2. Ensure you have the correct database connection');
        console.log('3. Run the initial migration if the table doesn\'t exist');
        return;
      }

      if (!sampleData || sampleData.length === 0) {
        console.log('✅ Products table exists but is empty');
        console.log('🔍 Cannot determine exact structure without data');
        
        // Try to create a test insert to see what fails
        console.log('🧪 Testing insert to identify missing columns...');
        const testProduct = {
          id: 'TEST_PRODUCT_' + Date.now(),
          name: 'Test Product',
          source_site: 'test',
          last_updated: new Date().toISOString(),
          last_checked: new Date().toISOString()
        };

        const { error: insertError } = await supabase
          .from('products')
          .insert(testProduct);

        if (insertError) {
          console.error('❌ Test insert failed:', insertError.message);
          analyzeInsertError(insertError.message);
        } else {
          console.log('✅ Test insert successful');
          // Clean up
          await supabase.from('products').delete().eq('id', testProduct.id);
        }
        
      } else {
        console.log('✅ Found sample data, analyzing structure...');
        const sample = sampleData[0];
        console.log('📋 Available columns in products table:');
        Object.keys(sample).forEach(col => {
          const value = sample[col];
          const type = typeof value;
          console.log(`  - ${col}: ${type} (${value === null ? 'NULL' : 'has value'})`);
        });
      }

    } else if (columns) {
      console.log('📋 Products table columns:');
      columns.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`);
      });
    }

    // Check if original_unit_qty exists
    console.log('\n🔍 Checking for specific column issues...');
    const { data: testOriginalUnit, error: origError } = await supabase
      .from('products')
      .select('original_unit_qty')
      .limit(1);

    if (origError && origError.message.includes('column "original_unit_qty" does not exist')) {
      console.log('❌ Column original_unit_qty is missing');
    } else {
      console.log('✅ Column original_unit_qty exists');
    }

  } catch (err) {
    console.error('❌ Unexpected error:', err.message);
  }
}

function analyzeInsertError(errorMessage) {
  console.log('\n🔍 Analyzing insert error...');
  
  if (errorMessage.includes('null value in column') && errorMessage.includes('violates not-null constraint')) {
    const match = errorMessage.match(/null value in column "([^"]+)"/);
    if (match) {
      const columnName = match[1];
      console.log(`❌ Column "${columnName}" has a NOT NULL constraint but we're not providing a value`);
      console.log(`💡 Solution: Either provide a value for "${columnName}" or make it nullable`);
    }
  }
  
  if (errorMessage.includes('column') && errorMessage.includes('does not exist')) {
    const match = errorMessage.match(/column "([^"]+)" does not exist/);
    if (match) {
      const columnName = match[1];
      console.log(`❌ Column "${columnName}" doesn't exist in the table`);
      console.log(`💡 Solution: Add the column or remove it from the insert statement`);
    }
  }
}

if (require.main === module) {
  checkDatabaseStructure();
}

module.exports = { checkDatabaseStructure };