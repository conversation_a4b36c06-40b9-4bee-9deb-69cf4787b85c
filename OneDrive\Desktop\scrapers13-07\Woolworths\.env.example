# Woolworths Scraper Configuration
# Copy this file to .env and fill in your values

# Supabase Configuration (Required for database mode)
SUPABASE_URL=https://emjwniuqwhvohjassnrg.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MDE2MDUsImV4cCI6MjA2Nzk3NzYwNX0.t4jObaNMtVPwTw_z5f2qygxN_QRyzBpr26kYaSuqsOU
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVtanduaXVxd2h2b2hqYXNzbnJnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjQwMTYwNSwiZXhwIjoyMDY3OTc3NjA1fQ.oTowRoNAjlUmk10O9pSMK2BvL0XlyBb5orVRrlEryHs

# Optional Configuration
STORE_NAME=Auckland Central

# Instructions:
# 1. Go to https://supabase.com/dashboard
# 2. Select your project → Settings → API
# 3. Copy the Project URL and API keys above
# 4. Create a Storage bucket named 'product-images' in your Supabase project
# 5. Save this file as .env (remove .example)