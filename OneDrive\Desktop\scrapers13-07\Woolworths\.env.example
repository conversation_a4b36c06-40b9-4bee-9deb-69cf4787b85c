# Woolworths Scraper Configuration
# Copy this file to .env and fill in your values

# Supabase Configuration (Required for database mode)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Optional Configuration
STORE_NAME=Auckland Central

# Instructions:
# 1. Go to https://supabase.com/dashboard
# 2. Select your project → Settings → API
# 3. Copy the Project URL and API keys above
# 4. Create a Storage bucket named 'product-images' in your Supabase project
# 5. Save this file as .env (remove .example)