import { MongoClient, Db, Collection, GridFSBucket, ObjectId } from "mongodb";
import * as dotenv from "dotenv";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

import { Product, UpsertResponse } from "./typings.js";
import { colour, log, logError } from "./utilities.js";

let client: MongoClient;
let db: Db;
let gridFS: GridFSBucket;
let storeId: ObjectId | undefined; // Woolworths store ObjectId

// Collections
let storesCollection: Collection;
let brandsCollection: Collection;
let consolidatedProductsCollection: Collection;
let priceHistoryCollection: Collection;
let categoryHierarchyCollection: Collection;

export async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }

  try {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    
    // Initialize collections
    storesCollection = db.collection("stores");
    brandsCollection = db.collection("brands");
    consolidatedProductsCollection = db.collection("consolidatedProducts");
    priceHistoryCollection = db.collection("priceHistory");
    categoryHierarchyCollection = db.collection("categoryHierarchy");
    
    // Initialize GridFS for image storage
    gridFS = new GridFSBucket(db, { bucketName: "productImages" });
    
    log(colour.green, "✅ MongoDB connection established");
    
    // Create indexes for performance
    await createIndexes();
    
  } catch (error: any) {
    logError(`Failed to connect to MongoDB: ${error.message}`);
    throw error;
  }
}

async function createIndexes() {
  try {
    // Consolidated products indexes
    await consolidatedProductsCollection.createIndex({ 
      "displayName": "text", 
      "normalizedName": "text", 
      "variants.storeName": "text" 
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({ 
      "variants.storeProductId": 1, 
      "variants.storeId": 1 
    });
    
    // Price history indexes
    await priceHistoryCollection.createIndex({ 
      "consolidatedProductId": 1, 
      "recordedAt": -1 
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    
    // Category hierarchy indexes
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    
    log(colour.blue, "✅ MongoDB indexes created");
  } catch (error: any) {
    logError(`Failed to create indexes: ${error.message}`);
  }
}

async function ensureStoreRow(): Promise<ObjectId> {
  if (storeId !== undefined) return storeId;
  
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }

    // Insert store if not exists
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    storeId = insertResult.insertedId;
    return storeId;
    
  } catch (error: any) {
    logError(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}

export async function upsertProductToMongoDB(scraped: Product): Promise<UpsertResponse> {
  if (!db) throw Error("MongoDB client not initialised");
  
  try {
    const sId = await ensureStoreRow();
    const now = new Date();
    
    // Find or create consolidated product
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    
    if (!consolidatedProduct) {
      // Create new consolidated product
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      
      const newProduct = {
        normalizedName,
        displayName: scraped.name,
        primarySize: scraped.size,
        categoryId: null, // TODO: Implement category mapping
        brandId: null, // TODO: Implement brand extraction
        matchConfidence: 100,
        manualMatch: false,
        variants: [{
          storeProductId: scraped.id,
          storeId: sId,
          storeName: scraped.name,
          storeSize: scraped.size,
          storeUnitPrice: scraped.unitPrice,
          storeUnitName: scraped.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        }],
        sizeVariants: scraped.size ? [{
          sizeName: scraped.size,
          sizeWeightGrams: null,
          sizeVolumeMl: null,
          isPrimarySize: true
        }] : [],
        currentPrices: [{
          storeId: sId,
          price: scraped.currentPrice,
          isSpecial: false,
          wasAvailable: true,
          recordedAt: now
        }],
        createdAt: now,
        updatedAt: now
      };
      
      const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
      consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };
      
    } else {
      // Update existing consolidated product
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    
    // Insert price history record
    await priceHistoryCollection.insertOne({
      consolidatedProductId: consolidatedProduct._id,
      storeId: sId,
      price: scraped.currentPrice,
      isSpecial: false,
      wasAvailable: true,
      recordedAt: now,
      year: now.getFullYear(),
      month: now.getMonth() + 1
    });
    
    log(colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return UpsertResponse.PriceChanged;
    
  } catch (error: any) {
    logError(`MongoDB upsert failed: ${error.message}`);
    return UpsertResponse.Failed;
  }
}

// Upload product image to MongoDB GridFS
export async function uploadImageToMongoDB(imageUrl: string, product: Product): Promise<boolean> {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");

  try {
    const sId = await ensureStoreRow();
    
    // Download image from Woolworths
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      logError(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }

    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    
    // Check if image already exists and delete it
    const existingFiles = await gridFS.find({ 
      "metadata.productId": product.id 
    }).toArray();
    
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    
    // Create upload stream
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: 'image/jpeg',
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: new Date()
      }
    });
    
    // Upload the image
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, (error) => {
        if (error) reject(error);
        else resolve(uploadStream.id);
      });
    });
    
    // Update product with image URL reference
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    
    log(colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;

  } catch (err: any) {
    logError(`Image upload error: ${err.message}`);
    return false;
  }
}

// Helper function to normalize product names for matching
function normalizeProductName(name: string, size?: string): string {
  let normalized = name.toLowerCase()
    .replace(/[^a-z0-9\s]/g, ' ')
    .replace(/\s+/g, '_')
    .trim();
  
  if (size) {
    const normalizedSize = size.toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .trim();
    normalized += '_' + normalizedSize;
  }
  
  return normalized;
}

// Get image stream from GridFS
export async function getImageFromGridFS(fileId: string): Promise<NodeJS.ReadableStream | null> {
  if (!gridFS) return null;
  
  try {
    const objectId = new ObjectId(fileId);
    return gridFS.openDownloadStream(objectId);
  } catch (error: any) {
    logError(`Failed to get image from GridFS: ${error.message}`);
    return null;
  }
}

// Close MongoDB connection
export async function closeMongoDB() {
  if (client) {
    await client.close();
    log(colour.blue, "MongoDB connection closed");
  }
}

// Health check
export async function mongoHealthCheck(): Promise<boolean> {
  try {
    await db.admin().ping();
    return true;
  } catch (error) {
    return false;
  }
}