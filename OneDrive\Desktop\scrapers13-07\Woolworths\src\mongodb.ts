import { MongoClient, Db, Collection, GridFSBucket, ObjectId } from "mongodb";
import * as dotenv from "dotenv";
dotenv.config();
dotenv.config({ path: `.env.local`, override: true });

import { Product, UpsertResponse } from "./typings.js";
import { colour, log, logError } from "./utilities.js";

let client: MongoClient;
let db: Db;
let gridFS: GridFSBucket;
let storeId: ObjectId | undefined; // Woolworths store ObjectId

// Collections
let storesCollection: Collection;
let brandsCollection: Collection;
let consolidatedProductsCollection: Collection;
let priceHistoryCollection: Collection;
let categoryHierarchyCollection: Collection;

export async function establishMongoDB() {
  const connectionString = process.env.MONGODB_CONNECTION_STRING;
  const databaseName = process.env.MONGODB_DATABASE_NAME || "nz-supermarket-scraper";
  
  if (!connectionString) {
    throw Error("MONGODB_CONNECTION_STRING not set in env");
  }

  try {
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db(databaseName);
    
    // Initialize collections
    storesCollection = db.collection("stores");
    brandsCollection = db.collection("brands");
    consolidatedProductsCollection = db.collection("consolidatedProducts");
    priceHistoryCollection = db.collection("priceHistory");
    categoryHierarchyCollection = db.collection("categoryHierarchy");
    
    // Initialize GridFS for image storage
    gridFS = new GridFSBucket(db, { bucketName: "productImages" });
    
    log(colour.green, "✅ MongoDB connection established");
    
    // Create indexes for performance
    await createIndexes();
    
  } catch (error: any) {
    logError(`Failed to connect to MongoDB: ${error.message}`);
    throw error;
  }
}

async function createIndexes() {
  try {
    // Consolidated products indexes
    await consolidatedProductsCollection.createIndex({ 
      "displayName": "text", 
      "normalizedName": "text", 
      "variants.storeName": "text" 
    });
    await consolidatedProductsCollection.createIndex({ "categoryId": 1 });
    await consolidatedProductsCollection.createIndex({ "brandId": 1 });
    await consolidatedProductsCollection.createIndex({ 
      "variants.storeProductId": 1, 
      "variants.storeId": 1 
    });
    
    // Price history indexes
    await priceHistoryCollection.createIndex({ 
      "consolidatedProductId": 1, 
      "recordedAt": -1 
    });
    await priceHistoryCollection.createIndex({ "year": 1, "month": 1 });
    
    // Category hierarchy indexes
    await categoryHierarchyCollection.createIndex({ "parentId": 1, "sortOrder": 1 });
    await categoryHierarchyCollection.createIndex({ "level": 1, "sortOrder": 1 });
    
    log(colour.blue, "✅ MongoDB indexes created");
  } catch (error: any) {
    logError(`Failed to create indexes: ${error.message}`);
  }
}

async function ensureStoreRow(): Promise<ObjectId> {
  if (storeId !== undefined) return storeId;
  
  try {
    const existingStore = await storesCollection.findOne({ storeId: "woolworths" });
    
    if (existingStore) {
      storeId = existingStore._id;
      return storeId;
    }

    // Insert store if not exists
    const insertResult = await storesCollection.insertOne({
      storeId: "woolworths",
      name: "Woolworths",
      logoUrl: null,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    storeId = insertResult.insertedId;
    return storeId;
    
  } catch (error: any) {
    logError(`Failed to ensure store row: ${error.message}`);
    throw error;
  }
}

export async function upsertProductToMongoDB(scraped: Product): Promise<UpsertResponse> {
  if (!db) throw Error("MongoDB client not initialised");
  
  try {
    const sId = await ensureStoreRow();
    const now = new Date();
    
    // Find or create consolidated product
    let consolidatedProduct = await consolidatedProductsCollection.findOne({
      "variants.storeProductId": scraped.id,
      "variants.storeId": sId
    });
    
    if (!consolidatedProduct) {
      // Try to find matching consolidated product using enhanced algorithm
      const normalizedName = normalizeProductName(scraped.name, scraped.size);
      log(colour.cyan, `🔍 Processing: ${scraped.name} -> normalized: ${normalizedName}`);

      const matchingProduct = await findBestMatch(normalizedName, scraped.size, scraped.name);

      if (matchingProduct) {
        // Found a match - add this as a new variant and add alias
        consolidatedProduct = matchingProduct;

        // Add current product name as alias if different
        await addProductAlias(consolidatedProduct._id, scraped.name);

        // Add new variant to existing consolidated product
        const newVariant = {
          storeProductId: scraped.id,
          storeId: sId,
          storeName: scraped.name,
          storeSize: scraped.size,
          storeUnitPrice: scraped.unitPrice,
          storeUnitName: scraped.unitName,
          lastSeen: now,
          isActive: true,
          imageUrl: null
        };

        await consolidatedProductsCollection.updateOne(
          { _id: consolidatedProduct._id },
          {
            $push: { variants: newVariant },
            $set: { updatedAt: now }
          }
        );

        log(colour.green, `✅ Added variant to existing product: ${matchingProduct.displayName}`);
      } else {
        // Create new consolidated product
        const newProduct = {
          normalizedName,
          displayName: scraped.name,
          primarySize: scraped.size,
          categoryId: null, // TODO: Implement category mapping
          brandId: null, // TODO: Implement brand extraction
          matchConfidence: 100,
          manualMatch: false,
          aliases: [], // Initialize empty aliases array
          variants: [{
            storeProductId: scraped.id,
            storeId: sId,
            storeName: scraped.name,
            storeSize: scraped.size,
            storeUnitPrice: scraped.unitPrice,
            storeUnitName: scraped.unitName,
            lastSeen: now,
            isActive: true,
            imageUrl: null
          }],
          sizeVariants: scraped.size ? [{
            sizeName: scraped.size,
            sizeWeightGrams: null,
            sizeVolumeMl: null,
            isPrimarySize: true
          }] : [],
          currentPrices: [{
            storeId: sId,
            price: scraped.currentPrice,
            isSpecial: false,
            wasAvailable: true,
            recordedAt: now
          }],
          createdAt: now,
          updatedAt: now
        };

        const insertResult = await consolidatedProductsCollection.insertOne(newProduct);
        consolidatedProduct = { _id: insertResult.insertedId, ...newProduct };

        log(colour.blue, `🆕 Created new consolidated product: ${scraped.name}`);
      }
    } else {
      // Update existing consolidated product
      await consolidatedProductsCollection.updateOne(
        { _id: consolidatedProduct._id },
        {
          $set: {
            "variants.$[variant].lastSeen": now,
            "variants.$[variant].storeUnitPrice": scraped.unitPrice,
            "variants.$[variant].storeUnitName": scraped.unitName,
            "currentPrices.$[price].price": scraped.currentPrice,
            "currentPrices.$[price].recordedAt": now,
            updatedAt: now
          }
        },
        {
          arrayFilters: [
            { "variant.storeProductId": scraped.id },
            { "price.storeId": sId }
          ]
        }
      );
    }
    
    // Insert price history record
    await priceHistoryCollection.insertOne({
      consolidatedProductId: consolidatedProduct._id,
      storeId: sId,
      price: scraped.currentPrice,
      isSpecial: false,
      wasAvailable: true,
      recordedAt: now,
      year: now.getFullYear(),
      month: now.getMonth() + 1
    });
    
    log(colour.green, `  Upserted: ${scraped.name.slice(0, 45).padEnd(45)} | $${scraped.currentPrice}`);
    return UpsertResponse.PriceChanged;
    
  } catch (error: any) {
    logError(`MongoDB upsert failed: ${error.message}`);
    return UpsertResponse.Failed;
  }
}

// Upload product image to MongoDB GridFS
export async function uploadImageToMongoDB(imageUrl: string, product: Product): Promise<boolean> {
  if (!gridFS) throw Error("MongoDB GridFS not initialised");

  try {
    const sId = await ensureStoreRow();
    
    // Download image from Woolworths
    const imageResponse = await fetch(imageUrl);
    if (!imageResponse.ok) {
      logError(`Failed to download image from ${imageUrl}: ${imageResponse.statusText}`);
      return false;
    }

    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
    
    // Check if image already exists and delete it
    const existingFiles = await gridFS.find({ 
      "metadata.productId": product.id 
    }).toArray();
    
    for (const file of existingFiles) {
      await gridFS.delete(file._id);
    }
    
    // Create upload stream
    const filename = `${product.id}.jpg`;
    const uploadStream = gridFS.openUploadStream(filename, {
      contentType: 'image/jpeg',
      metadata: {
        productId: product.id,
        storeId: sId,
        originalUrl: imageUrl,
        uploadedAt: new Date()
      }
    });
    
    // Upload the image
    await new Promise((resolve, reject) => {
      uploadStream.end(imageBuffer, (error) => {
        if (error) reject(error);
        else resolve(uploadStream.id);
      });
    });
    
    // Update product with image URL reference
    const imageUrl_gridfs = `gridfs://productImages/${uploadStream.id}`;
    
    await consolidatedProductsCollection.updateOne(
      { "variants.storeProductId": product.id },
      {
        $set: {
          "variants.$[variant].imageUrl": imageUrl_gridfs
        }
      },
      {
        arrayFilters: [{ "variant.storeProductId": product.id }]
      }
    );
    
    log(colour.blue, `  Image uploaded: ${product.id} -> GridFS ${uploadStream.id}`);
    return true;

  } catch (err: any) {
    logError(`Image upload error: ${err.message}`);
    return false;
  }
}

// Enhanced product name normalization with manual mappings
function normalizeProductName(name: string, size?: string): string {
  if (!name) return '';

  let normalized = name.toLowerCase()
    .replace(/'/g, '')
    .replace(/"/g, '')
    .replace(/-/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();

  // Remove common store descriptors
  const descriptors = ['woolworths', 'countdown', 'new world', 'paknsave', 'pak n save',
                      'select', 'premium', 'value', 'budget', 'signature', 'essentials',
                      'pams', 'homebrand', 'signature range', 'fresh choice'];

  for (const descriptor of descriptors) {
    normalized = normalized.replace(new RegExp(`\\b${descriptor}\\b`, 'g'), '').trim();
  }

  // Apply manual mappings for common product variations
  const manualMappings: Record<string, string> = {
    'coca cola': 'coke',
    'coca-cola': 'coke',
    'pepsi cola': 'pepsi',
    'tip top bread': 'tiptop bread',
    'anchor milk': 'anchor',
    'meadow fresh': 'meadowfresh',
    'chicken breast': 'chicken breast',
    'beef mince': 'beef mince'
  };

  for (const [original, replacement] of Object.entries(manualMappings)) {
    if (normalized.includes(original)) {
      normalized = normalized.replace(original, replacement);
    }
  }

  // Include size if provided
  if (size) {
    const normalizedSize = size.toLowerCase()
      .replace(/grams?/g, 'g')
      .replace(/kilograms?/g, 'kg')
      .replace(/litres?/g, 'l')
      .replace(/millilitres?/g, 'ml');
    normalized += ` ${normalizedSize}`;
  }

  return normalized.trim();
}

// Calculate Levenshtein distance for string similarity
function levenshteinDistance(s1: string, s2: string): number {
  if (!s1) return s2 ? s2.length : 0;
  if (!s2) return s1.length;

  const matrix: number[][] = [];

  for (let i = 0; i <= s2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= s1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= s2.length; i++) {
    for (let j = 1; j <= s1.length; j++) {
      const cost = s1[j - 1] === s2[i - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      );
    }
  }

  return matrix[s2.length][s1.length];
}

// Calculate similarity score between two product names
function calculateSimilarity(name1: string, name2: string): number {
  if (!name1 || !name2) return 0;
  if (name1 === name2) return 1.0;

  const distance = levenshteinDistance(name1, name2);
  const maxLength = Math.max(name1.length, name2.length);

  return maxLength === 0 ? 1.0 : 1.0 - distance / maxLength;
}

// Find best matching consolidated product using enhanced algorithm
async function findBestMatch(normalizedName: string, size: string | undefined, originalName: string): Promise<any> {
  try {
    // First try exact match
    const exactMatch = await consolidatedProductsCollection.findOne({
      normalizedName: normalizedName
    });

    if (exactMatch) {
      log(colour.green, `✅ Exact match found: ${exactMatch.displayName}`);
      return exactMatch;
    }

    // Try fuzzy matching
    const allProducts = await consolidatedProductsCollection.find({}).limit(1000).toArray();

    let bestMatch: any = null;
    let bestScore = 0;
    const threshold = 0.8;

    for (const product of allProducts) {
      let score = calculateSimilarity(normalizedName, product.normalizedName);

      // Check aliases if they exist
      if (product.aliases && Array.isArray(product.aliases)) {
        for (const alias of product.aliases) {
          const aliasScore = calculateSimilarity(normalizedName, alias);
          score = Math.max(score, aliasScore);
        }
      }

      if (score > bestScore && score >= threshold) {
        bestScore = score;
        bestMatch = product;
      }
    }

    if (bestMatch) {
      log(colour.yellow, `✅ Fuzzy match found: ${bestMatch.displayName} (confidence: ${bestScore.toFixed(3)})`);
      return bestMatch;
    }

    log(colour.red, `❌ No match found for: ${originalName}`);
    return null;
  } catch (error: any) {
    logError(`❌ Error finding match: ${error.message}`);
    return null;
  }
}

// Add alias to consolidated product
async function addProductAlias(consolidatedProductId: any, newAlias: string): Promise<void> {
  try {
    const normalizedAlias = normalizeProductName(newAlias);
    if (!normalizedAlias) return;

    const product = await consolidatedProductsCollection.findOne({ _id: consolidatedProductId });
    if (!product) return;

    // Get current aliases or initialize empty array
    const currentAliases = product.aliases || [];

    // Don't add if alias already exists or is the same as normalized name
    if (normalizedAlias === product.normalizedName || currentAliases.includes(normalizedAlias)) return;

    // Add new alias
    currentAliases.push(normalizedAlias);

    await consolidatedProductsCollection.updateOne(
      { _id: consolidatedProductId },
      {
        $set: {
          aliases: currentAliases,
          updatedAt: new Date()
        }
      }
    );

    log(colour.cyan, `📝 Added alias '${normalizedAlias}' to product ${consolidatedProductId}`);
  } catch (error: any) {
    logError(`❌ Error adding alias: ${error.message}`);
  }
}

// Get image stream from GridFS
export async function getImageFromGridFS(fileId: string): Promise<NodeJS.ReadableStream | null> {
  if (!gridFS) return null;

  try {
    const objectId = new ObjectId(fileId);
    return gridFS.openDownloadStream(objectId);
  } catch (error: any) {
    logError(`Failed to get image from GridFS: ${error.message}`);
    return null;
  }
}

// Close MongoDB connection
export async function closeMongoDB() {
  if (client) {
    await client.close();
    log(colour.blue, "MongoDB connection closed");
  }
}

// Health check
export async function mongoHealthCheck(): Promise<boolean> {
  try {
    await db.admin().ping();
    return true;
  } catch (error) {
    return false;
  }
}