#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Configuration for each scraper
const scrapers = {
  woolworths: {
    name: 'Woolworths',
    cwd: path.join(__dirname, 'Woolworths'),
    commands: {
      dev: ['npm', ['run', 'dev']],
      db: ['npm', ['run', 'db']],
      'db-images': ['npm', ['run', 'db', 'images']]
    }
  },
  newworld: {
    name: 'New World',
    cwd: path.join(__dirname, 'new-world/src'),
    commands: {
      dev: ['dotnet', ['run']],
      db: ['dotnet', ['run', 'db']],
      'db-images': ['dotnet', ['run', 'db', 'images']]
    }
  },
  paknsave: {
    name: 'PakNSave',
    cwd: path.join(__dirname, 'paknsave/src'),
    commands: {
      dev: ['dotnet', ['run']],
      db: ['dotnet', ['run', 'db']],
      'db-images': ['dotnet', ['run', 'db', 'images']]
    }
  }
};

// Parse command line arguments
const args = process.argv.slice(2);

// Handle help flag first
if (args.includes('--help') || args.includes('-h')) {
  showUsage();
  process.exit(0);
}

const mode = args[0] || 'dev'; // default to dev mode
const scraperNames = args.slice(1);

// Handle special commands
if (mode === 'check-schema') {
  console.log('🔍 Checking database schema...');
  const { checkAndFixSchema } = require('./fix-schema.js');
  checkAndFixSchema().then(() => process.exit(0)).catch(err => {
    console.error('❌ Schema check failed:', err.message);
    process.exit(1);
  });
  return;
}

// Validate mode
const validModes = ['dev', 'db', 'db-images'];
if (!validModes.includes(mode)) {
  console.error(`❌ Invalid mode: ${mode}`);
  console.error(`Valid modes: ${validModes.join(', ')}, check-schema`);
  process.exit(1);
}

// Determine which scrapers to run
let scrapersToRun;
if (scraperNames.length === 0) {
  scrapersToRun = Object.keys(scrapers); // Run all scrapers
} else {
  scrapersToRun = scraperNames.filter(name => scrapers[name]);
  const invalid = scraperNames.filter(name => !scrapers[name]);
  if (invalid.length > 0) {
    console.error(`❌ Invalid scraper names: ${invalid.join(', ')}`);
    console.error(`Valid scrapers: ${Object.keys(scrapers).join(', ')}`);
    process.exit(1);
  }
}

console.log(`🚀 Starting ${scrapersToRun.length} scraper(s) in ${mode} mode...`);
console.log(`📊 Scrapers: ${scrapersToRun.map(name => scrapers[name].name).join(', ')}`);
console.log('─'.repeat(60));

// Function to start a scraper
function startScraper(scraperKey) {
  const scraper = scrapers[scraperKey];
  const [command, commandArgs] = scraper.commands[mode];
  
  console.log(`🔄 Starting ${scraper.name} scraper...`);
  console.log(`   Command: ${command} ${commandArgs.join(' ')}`);
  console.log(`   Directory: ${scraper.cwd}`);
  
  const child = spawn(command, commandArgs, {
    cwd: scraper.cwd,
    stdio: ['inherit', 'pipe', 'pipe'],
    shell: process.platform === 'win32'
  });

  // Add colored output for each scraper
  const colors = {
    woolworths: '\x1b[32m', // Green
    newworld: '\x1b[34m',   // Blue
    paknsave: '\x1b[35m'    // Magenta
  };
  const reset = '\x1b[0m';
  const color = colors[scraperKey] || '\x1b[37m';

  child.stdout.on('data', (data) => {
    const lines = data.toString().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${color}[${scraper.name}]${reset} ${line}`);
      }
    });
  });

  child.stderr.on('data', (data) => {
    const lines = data.toString().split('\n');
    lines.forEach(line => {
      if (line.trim()) {
        console.log(`${color}[${scraper.name}]${reset} ❌ ${line}`);
      }
    });
  });

  child.on('close', (code) => {
    if (code === 0) {
      console.log(`${color}[${scraper.name}]${reset} ✅ Completed successfully`);
    } else {
      console.log(`${color}[${scraper.name}]${reset} ❌ Exited with code ${code}`);
    }
  });

  child.on('error', (error) => {
    console.log(`${color}[${scraper.name}]${reset} ❌ Error: ${error.message}`);
  });

  return child;
}

// Function to show usage
function showUsage() {
  console.log('🕷️  Supermarket Scraper Runner');
  console.log('');
  console.log('Usage: node start-scrapers.js [mode] [scrapers...]');
  console.log('');
  console.log('Modes:');
  console.log('  dev         - Dry run mode (console output only)');
  console.log('  db          - Scrape with database storage');
  console.log('  db-images   - Scrape with database storage + image upload');
  console.log('  check-schema - Check and fix database schema issues');
  console.log('');
  console.log('Scrapers:');
  console.log('  woolworths - Woolworths scraper');
  console.log('  newworld   - New World scraper');
  console.log('  paknsave   - PakNSave scraper');
  console.log('');
  console.log('Examples:');
  console.log('  node start-scrapers.js dev                    # Run all scrapers in dev mode');
  console.log('  node start-scrapers.js db woolworths          # Run only Woolworths in db mode');
  console.log('  node start-scrapers.js db-images newworld paknsave  # Run New World and PakNSave with images');
  console.log('  node start-scrapers.js check-schema           # Check and fix database schema');
  console.log('');
  console.log('Note: Use Ctrl+C to stop all running scrapers');
}


// Start all selected scrapers
const processes = [];
scrapersToRun.forEach((scraperKey, index) => {
  // Stagger the start times to avoid overwhelming the system
  setTimeout(() => {
    const child = startScraper(scraperKey);
    processes.push(child);
  }, index * 2000); // 2 second delay between each scraper
});

// Handle Ctrl+C to gracefully shut down all scrapers
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down all scrapers...');
  processes.forEach((child, index) => {
    if (!child.killed) {
      console.log(`   Stopping ${scrapersToRun[index]}...`);
      child.kill('SIGINT');
    }
  });
  
  setTimeout(() => {
    console.log('👋 All scrapers stopped');
    process.exit(0);
  }, 2000);
});

console.log('\n💡 Press Ctrl+C to stop all scrapers');