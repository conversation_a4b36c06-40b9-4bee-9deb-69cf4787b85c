-- Migration: Consolidated Products Schema
-- This migration creates a new schema for consolidated products across multiple stores

-- Create unified categories hierarchy
create table category_hierarchy (
  id          bigint generated always as identity primary key,
  name        text not null,
  parent_id   bigint references category_hierarchy(id),
  level       integer not null default 0, -- 0=main, 1=subcategory
  sort_order  integer default 0
);

-- Insert main categories
insert into category_hierarchy (name, level, sort_order) values
('Fresh Foods', 0, 1),
('Chilled & Frozen', 0, 2),
('Pantry & Dry Goods', 0, 3),
('Beverages', 0, 4),
('Health & Household', 0, 5);

-- Insert subcategories
insert into category_hierarchy (name, parent_id, level, sort_order) 
select 'Fruit & Vegetables', id, 1, 1 from category_hierarchy where name = 'Fresh Foods'
union all
select 'Meat & Poultry', id, 1, 2 from category_hierarchy where name = 'Fresh Foods'
union all
select 'Fish & Seafood', id, 1, 3 from category_hierarchy where name = 'Fresh Foods'
union all
select 'Bakery', id, 1, 4 from category_hierarchy where name = 'Fresh Foods'
union all
select 'Dairy & Deli', id, 1, 1 from category_hierarchy where name = 'Chilled & Frozen'
union all
select 'Frozen Foods', id, 1, 2 from category_hierarchy where name = 'Chilled & Frozen'
union all
select 'Desserts', id, 1, 3 from category_hierarchy where name = 'Chilled & Frozen'
union all
select 'Canned & Jarred', id, 1, 1 from category_hierarchy where name = 'Pantry & Dry Goods'
union all
select 'Pasta & Rice', id, 1, 2 from category_hierarchy where name = 'Pantry & Dry Goods'
union all
select 'Snacks & Confectionery', id, 1, 3 from category_hierarchy where name = 'Pantry & Dry Goods'
union all
select 'Baking & Cooking', id, 1, 4 from category_hierarchy where name = 'Pantry & Dry Goods'
union all
select 'Hot Drinks', id, 1, 1 from category_hierarchy where name = 'Beverages'
union all
select 'Cold Drinks', id, 1, 2 from category_hierarchy where name = 'Beverages'
union all
select 'Beer, Wine & Cider', id, 1, 3 from category_hierarchy where name = 'Beverages'
union all
select 'Health & Body', id, 1, 1 from category_hierarchy where name = 'Health & Household'
union all
select 'Baby & Toddler', id, 1, 2 from category_hierarchy where name = 'Health & Household'
union all
select 'Household & Cleaning', id, 1, 3 from category_hierarchy where name = 'Health & Household'
union all
select 'Pet Supplies', id, 1, 4 from category_hierarchy where name = 'Health & Household';

-- Create consolidated products table
create table consolidated_products (
  id                  uuid primary key default gen_random_uuid(),
  normalized_name     text not null,        -- Cleaned, standardized name for matching
  display_name        text not null,        -- Best display name chosen from variants
  primary_size        text,                 -- Most common size format
  category_id         bigint references category_hierarchy(id),
  brand_id            bigint references brands(id),
  
  -- Matching metadata
  match_confidence    integer default 100,  -- 0-100 confidence in product matching
  manual_match        boolean default false, -- If manually verified/adjusted
  
  -- Timestamps
  created_at          timestamptz default now(),
  updated_at          timestamptz default now()
);

create index on consolidated_products(category_id);
create index on consolidated_products(brand_id);
create index on consolidated_products(normalized_name);

-- Create table to link store-specific products to consolidated products
create table product_variants (
  id                     bigint generated always as identity primary key,
  consolidated_product_id uuid references consolidated_products(id) on delete cascade,
  store_product_id       text not null,     -- Original store-specific product ID (W123, N456, P789)
  store_id               bigint references stores(id),
  
  -- Store-specific details
  store_name             text not null,
  store_size             text,
  store_unit_price       numeric(10,2),
  store_unit_name        text,
  
  -- Metadata
  last_seen              timestamptz default now(),
  is_active              boolean default true,
  
  unique(store_product_id, store_id)
);

create index on product_variants(consolidated_product_id);
create index on product_variants(store_id);

-- Create consolidated prices table  
create table consolidated_prices (
  id                     bigint generated always as identity primary key,
  consolidated_product_id uuid references consolidated_products(id) on delete cascade,
  store_id               bigint references stores(id),
  price                  numeric(10,2) not null,
  recorded_at            timestamptz default now(),
  
  -- Price metadata
  is_special             boolean default false,
  was_available          boolean default true
);

create index on consolidated_prices(consolidated_product_id, store_id);
create index on consolidated_prices(recorded_at);

-- Create product size variants table for multiple sizes of same product
create table product_size_variants (
  id                     bigint generated always as identity primary key,
  consolidated_product_id uuid references consolidated_products(id) on delete cascade,
  size_name              text not null,
  size_weight_grams      integer,          -- Normalized weight for comparison
  size_volume_ml         integer,          -- Normalized volume for comparison
  is_primary_size        boolean default false,
  
  unique(consolidated_product_id, size_name)
);

-- Create view for easy querying of consolidated product data
create or replace view consolidated_product_details as
select 
  cp.id,
  cp.display_name,
  cp.primary_size,
  ch.name as category_name,
  ch_parent.name as main_category_name,
  b.name as brand_name,
  
  -- Current prices from all stores
  array_agg(
    json_build_object(
      'store_name', s.name,
      'price', latest_prices.price,
      'recorded_at', latest_prices.recorded_at,
      'is_available', latest_prices.was_available
    ) order by s.name
  ) filter (where latest_prices.price is not null) as store_prices,
  
  -- Size variants
  array_agg(distinct psv.size_name) filter (where psv.size_name is not null) as available_sizes,
  
  cp.created_at,
  cp.updated_at
from consolidated_products cp
left join category_hierarchy ch on cp.category_id = ch.id
left join category_hierarchy ch_parent on ch.parent_id = ch_parent.id
left join brands b on cp.brand_id = b.id
left join product_size_variants psv on cp.id = psv.consolidated_product_id
left join lateral (
  select distinct on (store_id) 
    store_id, price, recorded_at, was_available
  from consolidated_prices 
  where consolidated_product_id = cp.id
  order by store_id, recorded_at desc
) latest_prices on true
left join stores s on latest_prices.store_id = s.id
group by cp.id, cp.display_name, cp.primary_size, ch.name, ch_parent.name, b.name, cp.created_at, cp.updated_at;

-- Enable RLS
alter table consolidated_products enable row level security;
alter table product_variants enable row level security;
alter table consolidated_prices enable row level security;
alter table product_size_variants enable row level security;

-- Create policies for public read access
create policy "Public read access to consolidated_products"
  on consolidated_products for select using (true);

create policy "Public read access to product_variants"
  on product_variants for select using (true);

create policy "Public read access to consolidated_prices" 
  on consolidated_prices for select using (true);

create policy "Public read access to product_size_variants"
  on product_size_variants for select using (true);

-- Add helpful indexes for performance
create index on consolidated_prices(consolidated_product_id, recorded_at desc);
create index on product_variants(consolidated_product_id, is_active) where is_active = true;