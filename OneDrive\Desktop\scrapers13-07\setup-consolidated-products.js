#!/usr/bin/env node

/**
 * Setup Script for Consolidated Products System
 * 
 * This script helps you set up the new consolidated products system:
 * 1. Runs database migrations
 * 2. Installs required dependencies
 * 3. Sets up category mappings
 * 4. Provides migration guidance
 */

import { createClient } from '@supabase/supabase-js';
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();
dotenv.config({ path: '.env.local', override: true });

const COLORS = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(color, message) {
    console.log(`${color}${message}${COLORS.reset}`);
}

function logSection(title) {
    log(COLORS.cyan, `\n${'='.repeat(50)}`);
    log(COLORS.cyan, `${title}`);
    log(COLORS.cyan, `${'='.repeat(50)}`);
}

function logStep(step, description) {
    log(COLORS.yellow, `\n[${step}] ${description}`);
}

function logSuccess(message) {
    log(COLORS.green, `✅ ${message}`);
}

function logError(message) {
    log(COLORS.red, `❌ ${message}`);
}

function logWarning(message) {
    log(COLORS.yellow, `⚠️  ${message}`);
}

async function main() {
    logSection('Consolidated Products System Setup');
    
    // Check if running from correct directory
    if (!fs.existsSync('./supabase') && !fs.existsSync('./Woolworths')) {
        logError('Please run this script from the scrapers13-07 root directory');
        process.exit(1);
    }
    
    logStep(1, 'Checking environment variables');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
        logError('Missing required environment variables:');
        console.log('  SUPABASE_URL');
        console.log('  SUPABASE_SERVICE_ROLE_KEY (or SUPABASE_ANON_KEY)');
        console.log('\nPlease set these in your .env file or environment');
        process.exit(1);
    }
    
    logSuccess('Environment variables found');
    
    logStep(2, 'Installing Node.js dependencies');
    
    // Install shared dependencies
    try {
        if (!fs.existsSync('./shared/package.json')) {
            fs.mkdirSync('./shared', { recursive: true });
            fs.writeFileSync('./shared/package.json', JSON.stringify({
                "name": "scrapers-shared",
                "version": "1.0.0",
                "type": "module",
                "dependencies": {
                    "@supabase/supabase-js": "^2.38.0"
                }
            }, null, 2));
        }
        
        execSync('cd shared && npm install', { stdio: 'inherit' });
        logSuccess('Shared dependencies installed');
        
        // Install Woolworths dependencies if needed
        execSync('cd Woolworths && npm install @supabase/supabase-js', { stdio: 'inherit' });
        logSuccess('Woolworths dependencies updated');
        
    } catch (error) {
        logError('Failed to install dependencies: ' + error.message);
        process.exit(1);
    }
    
    logStep(3, 'Connecting to Supabase and running migrations');
    
    try {
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Test connection
        const { data, error } = await supabase.from('stores').select('count').limit(1);
        if (error) {
            throw new Error(`Connection failed: ${error.message}`);
        }
        
        logSuccess('Connected to Supabase');
        
        // Read and execute migration
        const migrationPath = './supabase/migrations/20240724_002_consolidated_products.sql';
        if (fs.existsSync(migrationPath)) {
            const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
            
            // Split by semicolon and execute each statement
            const statements = migrationSQL.split(';').filter(stmt => stmt.trim());
            
            for (const statement of statements) {
                if (statement.trim()) {
                    const { error } = await supabase.rpc('exec_sql', { sql: statement.trim() + ';' });
                    if (error && !error.message.includes('already exists')) {
                        console.log('Migration statement:', statement.substring(0, 100) + '...');
                        console.log('Error:', error.message);
                    }
                }
            }
            
            logSuccess('Database migration completed');
        } else {
            logWarning('Migration file not found - please run manually');
        }
        
    } catch (error) {
        logError('Database setup failed: ' + error.message);
        logWarning('You may need to run the migration manually in your Supabase dashboard');
    }
    
    logStep(4, 'Setting up API server (optional)');
    
    const apiSetup = `
# To set up the API server:
cd api
npm init -y
npm install express cors @supabase/supabase-js
node consolidated-products-api.js
`;
    
    console.log(apiSetup);
    
    logStep(5, 'Next Steps');
    
    const nextSteps = `
${COLORS.bright}Implementation Complete! Next steps:${COLORS.reset}

${COLORS.green}1. Database Schema:${COLORS.reset}
   ✅ Consolidated products tables created
   ✅ Category hierarchy established
   ✅ Product matching infrastructure ready

${COLORS.green}2. Scrapers Updated:${COLORS.reset}
   ✅ Woolworths scraper integrated with consolidated system
   🔄 New World & PakNSave scrapers need similar updates
   
${COLORS.green}3. Test the System:${COLORS.reset}
   • Run: cd Woolworths && npm run db
   • Check consolidated_products table for new entries
   • Verify product matching is working

${COLORS.green}4. Frontend Integration:${COLORS.reset}
   • Use the API endpoints in /api/consolidated-products-api.js
   • Implement ProductCard component (example provided)
   • Query consolidated_product_details view

${COLORS.green}5. Benefits You'll See:${COLORS.reset}
   ✨ Single product cards with multiple store prices
   ✨ Automatic product matching across stores
   ✨ Unified category structure
   ✨ Price comparison functionality
   ✨ Multiple size variants per product
   ✨ Clean, modern app interface

${COLORS.yellow}6. Optional Enhancements:${COLORS.reset}
   • Set up price alerts for products
   • Add product favorites/watchlists  
   • Implement shopping list functionality
   • Add price history charts
   • Create mobile app with push notifications

${COLORS.blue}Files Created:${COLORS.reset}
   📄 supabase/migrations/20240724_002_consolidated_products.sql
   📄 shared/product-matcher.js
   📄 Woolworths/src/consolidated-products.ts
   📄 api/consolidated-products-api.js
   📄 frontend-example/ProductCard.jsx
   📄 setup-consolidated-products.js

${COLORS.magenta}Need Help?${COLORS.reset}
   • Check the CLAUDE.md file for documentation
   • Review the API endpoints for frontend integration
   • Test with a small subset of products first
   • Monitor console logs for matching confidence scores
`;

    console.log(nextSteps);
    
    logSuccess('Setup completed successfully!');
}

// Handle errors gracefully
process.on('unhandledRejection', (error) => {
    logError('Unhandled error: ' + error.message);
    process.exit(1);
});

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}

export default main;