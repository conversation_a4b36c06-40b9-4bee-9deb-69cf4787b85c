# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a multi-store supermarket price scraping system for New Zealand supermarkets (Woolworths, New World, PakNSave). The system consists of:

### Scrapers
- **Woolworths scraper** (Node.js/TypeScript) - `Woolworths/src/index.ts`
- **New World scraper** (C#/.NET) - `new-world/src/Program.cs`  
- **PakNSave scraper** (C#/.NET) - `paknsave/src/Program.cs`

### Database Architecture
- **Current**: PostgreSQL via Supabase (see `supabase/migrations/20240713_001_init.sql`)
- **Schema**: `stores`, `brands`, `categories`, `products`, `prices` tables
- **Migration complete**: All scrapers now use Supabase exclusively

### Shared Components
- Each scraper uses Playwright for web automation
- Supabase client libraries for database operations
- Product overrides system for data quality (`ProductOverrides.txt` files)
- Rate limiting between page loads to avoid being blocked

## Common Development Commands

### Node.js (Woolworths)
```bash
cd Woolworths
npm install
npx playwright install  # Required on first setup
npm run dev          # Dry run mode (console output only)
npm run db           # Scrape with database storage
npm run db images    # Scrape with database + image upload
npm test             # Run Jest tests
```

### C# (.NET 6+)
```bash
cd paknsave         # or cd new-world/src
dotnet restore
dotnet build        # Build the project
pwsh bin/Debug/net6.0/playwright.ps1 install chromium  # Required on first setup
dotnet run          # Dry run mode
dotnet run db       # Scrape with database storage
dotnet run db images # Scrape with database + image upload
dotnet test         # Run tests (paknsave only)
```

**⚠️ Windows NuGet Issues**: If `dotnet restore` fails with NuGet source errors, run:
```cmd
fix-dotnet-issues.bat     # Command Prompt
# or
.\fix-dotnet-issues.ps1   # PowerShell
```

### Migration Scripts (Historical)
```bash
cd scripts
npm install
npm run export      # Historical data export utility
npm run import      # Historical data import utility
```

## Configuration

### Node.js Environment (.env)
```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
STORE_NAME=optional-store-location
```

### C# Configuration (appsettings.json)
```json
{
  "POSTGRES_CONNECTION": "Host=db.your-project.supabase.co;Port=5432;Database=postgres;Username=postgres;Password=your-password"
}
```

## Key Implementation Details

### Data Models
All scrapers use similar Product records with:
- Unique product ID per store
- Price history tracking with dates
- Unit pricing calculations
- Category classification
- Store-specific metadata

### Rate Limiting
- Woolworths: 7 second delay between pages
- New World: 11 second delay between pages
- PakNSave: 11 second delay between pages

### Product URL Management
- URLs stored in `urls.txt` files in each scraper directory
- Supports category-based URL organization
- Single URL testing via command line arguments

### Error Handling
- Robust retry mechanisms for network failures
- Product override system for data quality issues
- Detailed logging with colored console output

## Testing

### Woolworths (Jest)
- Tests located in `Woolworths/tests/`
- Configuration in `Woolworths/jest.config.js`
- Run with `npm test`

### PakNSave (MSTest)
- Tests in `paknsave/tests/`
- Includes Supabase and scraper unit tests  
- Run with `dotnet test`
- Uses MSTest framework, not XUnit

## Database Migration Notes

The system uses PostgreSQL/Supabase for data storage. Key features:

- All scrapers now use Supabase exclusively
- Real-time subscriptions and SQL querying available
- Foreign key constraints and indexes properly configured in PostgreSQL
- Historical data management scripts remain available in `scripts/` for reference

## Running All Scrapers

A unified script is available to start all scrapers separately:

```bash
# Check and fix database schema issues first (recommended before first run)
node start-scrapers.js check-schema

# Run all scrapers in dev mode (dry run)
node start-scrapers.js dev

# Run all scrapers with database storage
node start-scrapers.js db

# Run all scrapers with database + image upload
node start-scrapers.js db-images

# Run specific scrapers only
node start-scrapers.js dev woolworths
node start-scrapers.js db newworld paknsave

# Cross-platform convenience scripts
./start-scrapers.sh dev        # Unix/Linux
start-scrapers.bat dev         # Windows

# Get help
node start-scrapers.js --help
```

The script provides:
- Database schema validation and fixes
- Color-coded output for each scraper
- Staggered startup (2-second delays)
- Graceful shutdown with Ctrl+C
- Individual scraper selection
- Cross-platform compatibility

## Development Tips

- Use dry run mode (`npm run dev` or `dotnet run`) for testing without database writes
- Check product overrides files when data appears incorrect
- Monitor console output for detailed scraping progress and errors
- Use single URL testing for debugging specific products or pages
- Respect rate limits to avoid being blocked by target websites